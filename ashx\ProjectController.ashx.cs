using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Web;
using RFQ.Dao;
using RFQ.Dao.Model;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Web.SessionState;
using System.IO;
using OfficeOpenXml;
using System.Data;

namespace WebApplication1.ashx
{
    /// <summary>
    /// ProjectController 的摘要说明
    /// </summary>
    public class ProjectController : I<PERSON>ttpHandler, IRequiresSessionState
    {
        
        public void ProcessRequest(HttpContext context)
        {
            string action = context.Request.Params["action"].ToString();
            switch(action)
            {
                case "Query":
                    Query(context);
                    break;
                case "Update":
                    Update(context);
                    break;
                case "BatchDelete":
                    BatchDelete(context);
                    break;
                case "Add":
                    Add(context);
                    break;
                case "Upload":
                    Upload(context);
                    break;
                case "GetRFQNo":
                    GetRFQNo(context);
                    break;
                case "DownloadTemplate":
                    DownloadTemplate(context);
                    break;
                default:
                    break;
            }
        }

        public void Query(HttpContext context)
        {
            
            try
            {
                var rfqno = context.Request.Params["RFQNo"];
                //var customercode = context.Request.Params["CustomerCode"];
                //var customer = context.Request.Params["Customer"];
                //var endcustomer = context.Request.Params["EndCustomer"];
                var count = new BaseRepository<ProjectInfo>().GetCount(null);
                var result = new BaseRepository<ProjectInfo>().FindSingle(u=>u.RFQNo==rfqno);
                var type=new BaseRepository<TypeInfo>().Find(u => u.RFQ_NO == rfqno);
                //if (!String.IsNullOrEmpty(rfqno))
                //    result = result.Where(u => u.RFQNo.Contains(rfqno));
                //if (!String.IsNullOrEmpty(customercode))
                //    result = result.Where(u => u.CustomerCode.Contains(customercode));
                //if (!String.IsNullOrEmpty(customer))
                //    result = result.Where(u => u.Customer.Contains(customer));
                //if (!String.IsNullOrEmpty(endcustomer))
                //    result = result.Where(u => u.EndCustomer.Contains(endcustomer));
                //result = result.OrderBy(u=>u.RFQNo).Skip(int.Parse(context.Request["limit"])*(int.Parse(context.Request["page"])-1)).Take(int.Parse(context.Request["limit"]));
                dynamic d = new ExpandoObject();
                d.result = "success";
                
                d.project = result;
                d.types = type.ToList();
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
            catch (Exception ex)
            {
                dynamic d = new ExpandoObject();
                d.result = "fail";
                d.msg = ex.Message;
                d.data = null;
                d.count = 0;
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
        }

        public void Update(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {
                
                var rfqno = context.Request.Params["RFQNo"];
                var model= context.Request.Params["MODEL"];
                var modeldesc = context.Request.Params["MODEL_DESC"];
                var customercode = context.Request.Params["CustomerCode"];
                var customer = context.Request.Params["Customer"];
                var projectapp = context.Request.Params["ProjectApp"];
                var endcustomer = context.Request.Params["EndCustomer"];
                var designloc = context.Request.Params["DesignLoc"];
                var eau = context.Request.Params["EAU"];
                var costreduyear = context.Request.Params["CostReduYear"];
                var suppcur = context.Request.Params["SuppCur"];
                var rate=  context.Request.Params["Rate"];
                if(!String.IsNullOrEmpty(context.Request.Params["Rate"]) && !decimal.TryParse(context.Request.Params["Rate"], out decimal f))
                {
                    d.result = "fail";
                    d.msg = "汇率应为数字格式";
                    context.Response.Write(JsonConvert.SerializeObject(d));
                    return;
                }
                new BaseRepository<ProjectInfo>().Update(u => u.RFQNo == rfqno, u => new ProjectInfo
                {
                    //Model=model,
                    //ModelDesc=modeldesc,
                    CustomerCode=customercode,
                    Customer=customer,
                    ProjectApp=projectapp,
                    EndCustomer=endcustomer,
                    DesignLoc=designloc,
                    //EAU=int.TryParse(eau,out h)?int.Parse(eau):0,
                    CostReduYear=costreduyear,
                    SuppCur=suppcur,
                    Rate=Math.Round(decimal.Parse(rate), 2),
                });
                
                d.result = "success";
                d.msg = "修改成功";
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
            catch(Exception ex)
            {
                d.result = "fail";
                d.msg = ex.Message;
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
        }

        public void BatchDelete(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {
                string nos = context.Request["nos"];
                if (string.IsNullOrEmpty(nos))
                {
                    d.result = "error";
                    d.msg = "编号为空重新选择";
                    context.Response.Write(JsonConvert.SerializeObject(d));
                    return;
                }

                string[] Array = nos.Split(',');
                foreach(string ar in Array)
                {
                    new BaseRepository<ProjectInfo>().Delete(u => u.RFQNo==ar);
                }
                d.result = "success";
                d.msg = "删除成功";
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
            catch (Exception ex)
            {
                d.result = "error";
                d.msg = ex.Message;
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
        }

        public void Add(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {
                string RFQCustomerCode=context.Request.Params["RFQCustomerCode"];
                User userinfo = (User)HttpContext.Current.Session["UserInfo"];
                var pro = new BaseRepository<ProjectInfo>().FindSingle(u => u.RFQCustomerCode == RFQCustomerCode);
                if (pro == null)
                {
                    string No = "";
                    ProjectInfo pi = new ProjectInfo();
                    //string date = "20240618";
                    JArray jArray = (JArray)JsonConvert.DeserializeObject(context.Request.Params["modelinfo"]);
                    string date = DateTime.Now.ToString("yyyyMMdd");
                    var serialnum = new BaseRepository<ProjectInfo>().Find(u => u.RFQNo.Contains(date)).OrderByDescending(u => u.RFQNo).FirstOrDefault();
                    No = GetSerialNumber(serialnum, context.Request.Params["CustomerCode"]);
                    
                    pi.RFQNo = No;

                    pi.CustomerCode = context.Request.Params["CustomerCode"];
                    pi.RFQCustomerCode = context.Request.Params["rfqcustomercode"];
                    pi.Customer = context.Request.Params["Customer"];
                    pi.ProjectApp = context.Request.Params["ProjectApp"];
                    pi.EndCustomer = context.Request.Params["EndCustomer"];
                    pi.DesignLoc = context.Request.Params["DesignLoc"];
                    pi.CostReduYear = context.Request.Params["CostReduYear"];
                    pi.SuppCur = context.Request.Params["SuppCur"];
                    if(!String.IsNullOrEmpty(context.Request.Params["Rate"]) && !decimal.TryParse(context.Request.Params["Rate"], out decimal f))
                    {
                        d.result = "fail";
                        d.msg = "汇率应为数字格式";
                        context.Response.Write(JsonConvert.SerializeObject(d));
                        return;
                    }
                    pi.Rate = Math.Round(decimal.Parse(context.Request.Params["Rate"]), 2);
                    pi.Status = "1";  //流程状态标识 1表示录入项目信息完成
                    pi.CreateTime = DateTime.Now;
                    pi.CreateUser = userinfo.UserName;
                    int i = 1;
                    foreach (var mi in jArray)
                    {
                        
                        TypeInfo ti = new TypeInfo();
                        JObject j = JObject.Parse(mi.ToString());
                        ti.RFQ_NO = No;
                        ti.MODEL = j["MODEL"].ToString();
                        ti.MODEL_DESC = j["MODEL_DESC"].ToString();
                        ti.EAU1 = int.TryParse(j["EAU1"].ToString(), out int h1) ? h1 : 0;
                        ti.EAU2 = int.TryParse(j["EAU2"].ToString(), out int h2) ? h2 : 0;
                        ti.EAU3 = int.TryParse(j["EAU3"].ToString(), out int h3) ? h3 : 0;
                        ti.CREATE_TIME = DateTime.Now;
                        ti.BOMQTYFIELD = "QPA" + i;
                        new BaseRepository<TypeInfo>().Add(ti);
                        i++;

                    }
                    new BaseRepository<ProjectInfo>().Add(pi);
                    HttpContext.Current.Session["RFQNo"] = No;
                    d.result = "success";
                    d.msg = "添加成功";
                    d.data = No;
                }
                else
                {
                    d.result = "fail";
                    d.msg = "已存在该项目信息！";
                }
                
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
            catch (Exception ex)
            {
                d.result = "fail";
                d.msg = ex.Message;
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
        }

        public void GetRFQNo(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {

                
                var Nolist=new RFQDBContext().ProjectInfos.Select(u => u.RFQNo).Distinct().ToList();
                
                d.data = Nolist;
                d.result = "success";
                d.msg = "获取RFQ编号成功";
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
            catch (Exception ex)
            {
                d.data = null;
                d.result = "error";
                d.msg = ex.Message;
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
        }

        public void Upload(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {
                // 检查是否有上传文件
                if (context.Request.Files.Count > 0)
                {
                    var file = context.Request.Files[0];


                    // 保存上传的文件到服务器临时目录
                    string filePath = Path.Combine(@"\\172.20.0.14\System Static\RFQSystem", Path.GetFileName(file.FileName));
                    file.SaveAs(filePath);

                    // 读取 Excel 文件内容
                    var exceldata = ReadFromExcel(filePath);
                    if(exceldata.result.Contains("FAIL"))
                    {
                        
                        context.Response.Write(JsonConvert.SerializeObject(exceldata));
                        return;
                    }
                    //var pro = new BaseRepository<ProjectInfo>().FindSingle(u => u.CustomerCode == exceldata.project.CustomerCode &&
                    //u.ProjectApp == exceldata.project.ProjectApp && u.EndCustomer == exceldata.project.EndCustomer);
                    //if (pro == null)
                    //{
                    //    new BaseRepository<ProjectInfo>().Add(exceldata.project);
                    //    new BaseRepository<TypeInfo>().BatchAdd(exceldata.types.ToArray());
                    //}


                    // 返回读取的内容

                    context.Response.Write(JsonConvert.SerializeObject(exceldata));


                    // 删除临时文件
                    File.Delete(filePath);


                }
                else
                {
                    d.result = "FAIL:没有文件上传";
                    context.Response.Write(JsonConvert.SerializeObject(d));
                }
            }
            catch (Exception ex)
            {
                d.result = "error"+ex.Message;
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
        }

        public exceldata ReadFromExcel(string path)
        {
            exceldata ex = new exceldata();
            User userinfo = (User)HttpContext.Current.Session["UserInfo"];
            try
            {
                ProjectInfo pj = new ProjectInfo();
                var typelist = new List<TypeInfo>();
                decimal u;
                int h;
                String No;
                string date = DateTime.Now.ToString("yyyyMMdd");
                var serialnum = new BaseRepository<ProjectInfo>().Find(p => p.RFQNo.Contains(date)).OrderByDescending(p => p.RFQNo).FirstOrDefault();
                
                using (var excelPack = new ExcelPackage())
                {
                    //Load excel stream
                    using (var stream = File.OpenRead(path))
                    {
                        excelPack.Load(stream);
                    }
                    //处理第一个工作表。(如果处理多个表格，可以在此用for循环处理)
                    var ws = excelPack.Workbook.Worksheets[0];
                    DataTable excelasTable = new DataTable();
                    int startrow = 0,endrow=0;
                    if (ws.Cells[1, 1].Text != "客户编号")
                    {
                        ex.result = "FAIL:非标准模版";
                        return ex;
                    }
                        
                    pj.CustomerCode = ws.Cells[1, 2].Text;
                    pj.Customer = ws.Cells[2, 2].Text;
                    pj.EndCustomer = ws.Cells[3, 2].Text;
                    pj.ProjectApp = ws.Cells[4, 2].Text;
                    pj.DesignLoc= ws.Cells[5, 2].Text;
                    pj.CostReduYear= ws.Cells[6, 2].Text;
                    pj.SuppCur = ws.Cells[7, 2].Text;
                    if(!String.IsNullOrEmpty(ws.Cells[8, 2].Text) && !decimal.TryParse(ws.Cells[8, 2].Text, out u))
                    {
                        ex.result = "FAIL:汇率应为数字格式";
                        return ex;
                    }
                    pj.Rate = Math.Round(decimal.Parse(ws.Cells[8, 2].Text), 2);
                    pj.RFQCustomerCode = ws.Cells[9, 2].Text;
                    pj.Status = "1";  //流程状态标识 1表示录入项目信息完成
                    pj.CreateTime = DateTime.Now;
                    pj.CreateUser = userinfo.UserName;
                    No = GetSerialNumber(serialnum, pj.CustomerCode);
                    pj.RFQNo = No;
                    foreach (var firstRowCell in ws.Cells[1, 1, ws.Dimension.End.Row, 1])
                    {
                        if (!string.IsNullOrEmpty(firstRowCell.Text) && firstRowCell.Text.Trim() == "型号")
                        {
                            startrow = firstRowCell.Start.Row + 1;
                        }
                        if(string.IsNullOrEmpty(firstRowCell.Text))
                        {
                            endrow = firstRowCell.Start.Row;
                            break;
                        }
                    }
                    if (endrow == 0)
                        endrow = ws.Dimension.End.Row + 1;
                    for (int rowNum = startrow; rowNum <= endrow - 1; rowNum++)
                    {
                        var t = new TypeInfo();
                        t.RFQ_NO = No;
                        t.MODEL = ws.Cells[rowNum, 1].Text;
                        t.MODEL_DESC = ws.Cells[rowNum, 2].Text;
                        if (!String.IsNullOrEmpty(ws.Cells[rowNum, 3].Text) && !int.TryParse(ws.Cells[rowNum, 3].Text, out h))
                        {
                            ex.result = "FAIL:EAU应为数字格式";
                            return ex;
                        }
                        t.EAU1 = int.TryParse(ws.Cells[rowNum, 3].Text, out h) ? int.Parse(ws.Cells[rowNum, 3].Text) : 0;
                        if (!String.IsNullOrEmpty(ws.Cells[rowNum, 4].Text) && !int.TryParse(ws.Cells[rowNum, 4].Text, out h))
                        {
                            ex.result = "FAIL:EAU应为数字格式";
                            return ex;
                        }
                        t.EAU2 = int.TryParse(ws.Cells[rowNum, 4].Text, out h) ? int.Parse(ws.Cells[rowNum, 4].Text) : 0;
                        if (!String.IsNullOrEmpty(ws.Cells[rowNum, 5].Text) && !int.TryParse(ws.Cells[rowNum, 5].Text, out h))
                        {
                            ex.result = "FAIL:EAU应为数字格式";
                            return ex;
                        }
                        t.EAU3 = int.TryParse(ws.Cells[rowNum, 5].Text, out h) ? int.Parse(ws.Cells[rowNum, 5].Text) : 0;
                        typelist.Add(t);
                        //var wsRow = ws.Cells[rowNum, 1, rowNum, excelasTable.Columns.Count];
                        //DataRow row = excelasTable.Rows.Add();
                        //foreach (var cell in wsRow)
                        //{
                        //    row[cell.Start.Column - 1] = cell.Text;
                        //}
                    }
                    //将所有内容作为泛型获取，最终定是否强制转换为所需类型
                    //var generatedType = JsonConvert.DeserializeObject<T>(JsonConvert.SerializeObject(excelasTable));
                    //return (T)Convert.ChangeType(generatedType, typeof(T));
                    ex.project = pj;
                    ex.types = typelist;
                    ex.result = "SUCCESS:上传成功";

                    return ex;
                }
            }
            catch (Exception exception)
            {
                ex.result = "FAIL:"+exception.Message;
                return ex;
            }
        }

        

        public bool IsReusable
        {
            get
            {
                return false;
            }
        }

        public class exceldata
        {
            /// <summary>
            /// 权限菜单树
            /// </summary>
            public List<TypeInfo> types { get; set; }

            /// <summary>
            /// logo
            /// </summary>
            public ProjectInfo project { get; set; }

            public string result { get; set; }


        }

        public string GetSerialNumber(ProjectInfo serialNumber,string customercode)
        {
            if (serialNumber!= null)
            {
                string headDate = serialNumber.RFQNo.Substring(serialNumber.RFQNo.Length - 12, 8);
                int lastNumber = int.Parse(serialNumber.RFQNo.Substring(serialNumber.RFQNo.Length - 3));
                //如果数据库最大值流水号中日期和生成日期在同一天，则顺序号加1
                if (headDate == DateTime.Now.ToString("yyyyMMdd"))
                {
                    lastNumber++;
                    return "RFQ-"+customercode+"-"+headDate +"-"+ lastNumber.ToString("000");
                }
            }
            return "RFQ-" + customercode + "-" + DateTime.Now.ToString("yyyyMMdd") + "-" + "001";
        }

        public void DownloadTemplate(HttpContext context)
        {
            try
            {
                // 模板文件名
                string templateFileName = "批量上传.xlsx";

                // 尝试多个可能的文件路径
                string[] possiblePaths = {
                    // 项目根目录
                    context.Server.MapPath("~/" + templateFileName),
                    // Flow目录的上级目录
                    context.Server.MapPath("~/../" + templateFileName),
                    // 网络共享路径
                    System.IO.Path.Combine(@"\\172.20.0.14\System Static\RFQSystem", templateFileName),
                    // 当前应用程序目录
                    System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, templateFileName)
                };

                string filePath = null;
                foreach (string path in possiblePaths)
                {
                    if (System.IO.File.Exists(path))
                    {
                        filePath = path;
                        break;
                    }
                }

                if (string.IsNullOrEmpty(filePath))
                {
                    context.Response.ContentType = "text/plain; charset=utf-8";
                    context.Response.Write("模板文件不存在，请联系管理员");
                    return;
                }

                // 获取文件信息
                System.IO.FileInfo fileInfo = new System.IO.FileInfo(filePath);

                // 设置响应头
                context.Response.Clear();
                context.Response.ClearHeaders();
                context.Response.ClearContent();
                context.Response.AddHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                context.Response.AddHeader("Content-Disposition", $"attachment; filename*=UTF-8''{System.Web.HttpUtility.UrlEncode("批量上传模板.xlsx")}");
                context.Response.AddHeader("Content-Length", fileInfo.Length.ToString());

                // 输出文件内容
                using (System.IO.FileStream fs = new System.IO.FileStream(filePath, System.IO.FileMode.Open, System.IO.FileAccess.Read))
                {
                    byte[] buffer = new byte[64 * 1024]; // 64KB buffer
                    int read;
                    while ((read = fs.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        context.Response.OutputStream.Write(buffer, 0, read);
                    }
                    context.Response.Flush();
                }
                context.Response.End();
            }
            catch (Exception ex)
            {
                context.Response.ContentType = "text/plain; charset=utf-8";
                context.Response.Write("下载失败：" + ex.Message);
            }
        }

    }
}