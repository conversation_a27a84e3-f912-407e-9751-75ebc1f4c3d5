﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="CreateRfq.aspx.cs" Inherits="WebApplication1.Flow.CreateRfq" %>

<!DOCTYPE html>
<head runat="server">
    <meta charset="utf-8">
    <title>新建RFQ流程</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../lib/layui-v2.9.21/css/layui.css" media="all">
    <link rel="stylesheet" href="../css/public.css" media="all">
<style>
    .container{
        display:flex;
        justify-content:center;
        align-items:center;
    }
    .layui-form-label{
        width:180px !important; 
    }
    </style>
</head>
<div class="layuimini-container">
    <div class="layuimini-main">
        <fieldset class="table-search-fieldset">
           <legend>基本信息</legend>
            <div style="margin: 20px 10px 10px 10px">
                <form class="layui-form layui-form-pane">
                    <div class="layui-form-item">
                        
                        <div class="layui-inline">
                            <label class="layui-form-label">客户编号<label style="color:red">*</label></label>
                            <div class="layui-input-inline">
                                <input type="text" name="CustomerCode" autocomplete="off" class="layui-input" lay-verify="required">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">RFQ客户编码<label style="color:red">*</label></label>
                            <div class="layui-input-inline">
                                <input type="text" name="RFQCustomerCode" autocomplete="off" class="layui-input" lay-verify="required">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">客户名<label style="color:red">*</label></label>
                            <div class="layui-input-inline">
                                <input type="text" name="Customer" autocomplete="off" class="layui-input" lay-verify="required">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">终端客户<label style="color:red">*</label></label>
                            <div class="layui-input-inline">
                                <input type="text" name="EndCustomer" autocomplete="off" class="layui-input" lay-verify="required">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">项目应用<label style="color:red">*</label></label>
                            <div class="layui-input-inline">
                                <input type="text" name="ProjectApp" autocomplete="off" class="layui-input" lay-verify="required">
                            </div>
                        </div><br>
                        <div class="layui-inline">
                            <label class="layui-form-label">设计地<label style="color:red">*</label></label>
                            <div class="layui-input-inline">
                                <input type="text" name="DesignLoc" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">达成的年降价比例<label style="color:red">*</label></label>
                            <div class="layui-input-inline" style="position: relative;">
                                <input type="text" name="CostReduYear" autocomplete="off" class="layui-input" style="padding-right: 25px;">
                                <span style="position: absolute; right: 8px; top: 50%; transform: translateY(-50%); color: #999;">%</span>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">报价币别<label style="color:red">*</label></label>
                            <div class="layui-input-inline">
                                <select name="SuppCur" lay-verify="required" lay-filter="SuppCur" lay-search="">
                                <option value="">直接选择或搜索选择</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">报价汇率</label>
                            <div class="layui-input-inline">
                                <input type="text" name="Rate" autocomplete="off" class="layui-input">
                            </div>  
                        </div><br>
                        <div style="width:100%;text-align:center">
                        <div class="layui-inline">
                            <button type="button" id="upload" class="layui-btn layui-btn-primary" lay-filter="data-upload-btn">批量上传</button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <a id="download" class="layui-btn layui-btn-primary" href="../批量上传.xlsx" download="批量上传模板.xlsx">下载模板</a>
                        </div>

                            </div>
                    </div>
                </form>
            </div>
        </fieldset>
        

        <script type="text/html" id="toolbarDemo">
            <div class="layui-btn-container">
                <button class="layui-btn layui-btn-normal layui-btn-sm data-add-btn" lay-event="add"> 添加 </button>
                <button class="layui-btn layui-btn-sm layui-btn-danger data-delete-btn" lay-event="delete"> 删除 </button>
            </div>
        </script>

        <table class="layui-hide" id="currentTableId" lay-filter="currentTableFilter"></table>

        
        <script type="text/html" id="currentTableBar">



            <a class="layui-btn layui-btn-normal layui-btn-xs data-count-edit" lay-event="edit">编辑</a>
            <a class="layui-btn layui-btn-xs layui-btn-danger data-count-delete" lay-event="delete">删除</a>
        </script>
        <div class="container">
        <div class="layui-input-block">
            <button type="submit" id="nextstep" class="layui-btn" lay-submit lay-filter="data-next-btn">
                &emsp;下一步&emsp;
                                           
            </button>
        </div>
    </div>
    </div>
</div>
<script src="../lib/layui-v2.9.21/layui.js" charset="utf-8"></script>
<script src="../js/guid.js" charset="utf-8"></script>
<script src="../js/navigate.js" charset="utf-8"></script>
<script>

    layui.use(['form', 'table', 'upload', 'element'], function () {
        var $ = layui.jquery,
            form = layui.form,
            table = layui.table,
            upload = layui.upload,
            counttable = 0,
            addData = [];
        form.render();

        // 为报价汇率和年降价比例添加输入验证
        $("input[name='Rate'], input[name='CostReduYear']").on('input', function() {
            var $this = $(this);
            var value = $this.val();

            // 如果值不为空，并且不是一个有效的数字（允许整数或小数）
            // 有效的模式: 123, 123. , 123.45 , .45
            // 无效的模式: abc, 1.2.3, 12a, .
            if (value && !/^(\d+(\.\d*)?|\.\d+)$/.test(value) && value !== "") {
                 // 特殊处理，如果用户只输入了一个点，允许他们继续输入数字
                if (value === '.') { 
                    return; 
                }
                layer.msg('请输入有效的数字', { icon: 5, time: 2000 });
                $this.val(''); // 清空输入框
            }
        });

        // 页面加载时获取币别列表
        function loadCurrencyOptions() {
            $.ajax({
                url: '../ashx/ExchangeRate.ashx/getList',
                type: 'get',
                success: function (res) {
                    if (res.code === 0) {
                        var currencySelect = $("select[name='SuppCur']");
                        currencySelect.empty();
                        currencySelect.append('<option value="">直接选择或搜索选择</option>');

                        // 添加币别选项
                        res.data.forEach(function (currency) {
                            currencySelect.append(
                                `<option value="${currency.CURRENCY}">${currency.CURRENCY}</option>`
                            );
                        });

                        // 重新渲染表单
                        form.render('select');
                    } else {
                        layer.msg('获取币别列表失败：' + res.msg);
                    }
                },
                error: function () {
                    layer.msg('获取币别列表失败，请检查网络连接');
                }
            });
        }

        // 监听币别选择变化
        form.on('select(SuppCur)', function (data) {
            if (data.value) {
                $.ajax({
                    url: '../ashx/ExchangeRate.ashx/GetRate',
                    type: 'get',
                 data:{ currency: data.value },
                    success: function (res) {
                        if (res.code === 0) {
                            // 设置汇率值
                            $("input[name='Rate']").val(res.data.RATE);
                        } else {
                            layer.msg('获取汇率失败：' + res.msg);
                        }
                    },
                    error: function () {
                        layer.msg('获取汇率失败，请检查网络连接');
                    }
            });
    } else {
        // 清空汇率
        $("input[name='Rate']").val('');
    }
    });

    // 页面初始化时加载币别列表
    loadCurrencyOptions();

    

        table.render({
            elem: '#currentTableId',
            data: addData,
            toolbar: '#toolbarDemo',
            method:'post',
            defaultToolbar: ['filter', 'exports', 'print', {
                title: '提示',
                layEvent: 'LAYTABLE_TIPS',
                icon: 'layui-icon-tips'
            }],
            cols: [[
                { type: "checkbox", width: 50 },
                {
                    field: 'number', title: '序号', templet: function (d) {
                        return d.LAY_INDEX+1;
                    }
                },
                { field: 'MODEL', width: 150, title: '型号', sort: true },
                { field: 'MODEL_DESC', width: 250, title: '型号描述' },
                { field: 'EAU1', width: 150, title: 'EAU1', sort: true },
                { field: 'EAU2', width: 150, title: 'EAU2', sort: true },
                { field: 'EAU3', width: 150, title: 'EAU3', sort: true },
                { title: '操作', minWidth: 150, toolbar: '#currentTableBar', align: "center" },
                { field: 'Id', width: 150, title: 'guid', hide:true}
            ]],
            limits: [10, 15, 20, 25, 50, 100],
            limit: 10,
            page: true,
            skin: 'line',
            done: function (res,curr,count,orign) {
                counttable = count;
            }
            
        });

        // 获取 URL 参数
        function getUrlParameter(name) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(window.location.href);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }

        // 页面加载完成后，获取 URL 参数并填充到输入框
        $(document).ready(function () {
            console.log()
            var rfqNo = getUrlParameter('RFQNo');
            if (rfqNo) {
                $.ajax({
                    url: '../ashx/ProjectController.ashx?action=Query&RFQNo=' + rfqNo,
                    type: 'post',
                    success: function (response) {
                        // 上传完毕回调
                        var res = eval("(" + response + ")");
                        console.log(res.result);
                        layer.closeAll('loading'); // 关闭加载层
                        $('button').prop('disabled', false);
                        if (res.result== 'success') {
                            
                            addData = res.types;
                            table.reload('currentTableId', { data: addData });
                            //var project_info = eval("(" + res.project + ")");
                            console.log(res.project);
                            //后台赋值给界面
                            $("input[name='CustomerCode']").val(res.project.CustomerCode);
                            $("input[name='RFQCustomerCode']").val(res.project.RFQCustomerCode);
                            $("input[name='Customer']").val(res.project.Customer);
                            $("input[name='ProjectApp']").val(res.project.ProjectApp);
                            $("input[name='EndCustomer']").val(res.project.EndCustomer);
                            $("input[name='DesignLoc']").val(res.project.DesignLoc);
                            $("input[name='CostReduYear']").val(res.project.CostReduYear);
                            $("input[name='Rate']").val(res.project.Rate);
                            $("select[name='SuppCur']").val(res.project.SuppCur);
                            form.render();
                        } else {
                            layer.msg('加载数据失败' + res.msg);
                        }

                    },
                    error: function (data) {
                        layer.alert(objdata.msg);
                    }
                });
            }
        });

        

        form.on('submit(data-next-btn)', function (data) {
            var rfqNo = getUrlParameter('RFQNo');

            if ($("input[name='CustomerCode']").val() == "" || $("input[name='RFQCustomerCode']").val() == "" || $("input[name='Customer']").val() == "" || $("input[name='ProjectApp']").val() == ""
                || $("input[name='DesignLoc']").val() == "" || $("input[name='EndCustomer']").val() == "" || $("select[name='SuppCur']").val() == "" || $("input[name='CostReduYear']").val() == "") {
                layer.msg('请填写必填项', { icon: 0 });
                return;
            }
            if (counttable == 0) {
                layer.msg('未填写型号信息', { icon: 0 });
                return;
            }
            if (rfqNo) {
                navigateInIframe("../Flow/CustomerTrans.aspx?RFQNo=" + rfqNo, "客户模板转换-" + rfqNo);
                return;
            }
            $.ajax({
                url: '../ashx/ProjectController.ashx?action=Add',
                type: 'post',
                data: {
                    customercode: $("input[name='CustomerCode']").val(), rfqcustomercode: $("input[name='RFQCustomerCode']").val(), customer: $("input[name='Customer']").val(), projectapp: $("input[name='ProjectApp']").val(),
                    designloc: $("input[name='DesignLoc']").val(), suppcur: $("select[name='SuppCur']").val(), costreduyear: $("input[name='CostReduYear']").val(),
                    rate: $("input[name='Rate']").val(), endcustomer:$("input[name='EndCustomer']").val(),modelinfo: JSON.stringify(addData)
                },
                success: function (data) {
                    console.log(data);
                    var objdata = eval("(" + data + ")")
                    if (objdata.result == "success") {
                        //console.log(field);
                        layer.msg(objdata.msg, { icon: 1, time:
                             2000 }, function () {
                            // 使用完全替换的方式导航
                            navigateInIframe("../Flow/CustomerTrans.aspx?RFQNo=" + objdata.data, "客户模板转换-" + objdata.data);
                            console.log("客户模板转换");
                        })
                    }
                    else {
                        layer.alert(objdata.msg);
                    }

                },
                error: function (data) {
                    layer.alert(objdata.msg);
                }
            });
        });

        

        
        // 上传实例
        upload.render({
            elem: '#upload',
            url: '../ashx/ProjectController.ashx?action=Upload', // 后台处理上传的接口
            accept: 'file', // 普通文件
            exts: 'xls|xlsx', // 只允许上传Excel文件
            before: function () {
                console.log('上传开始');
                // 禁用所有按钮
                $('button').prop('disabled', true);
                layer.load(); // 显示加载层
            },
            done: function (res) {
                // 上传完毕回调
                console.log('上传完成，返回结果：', res);
                layer.closeAll('loading'); // 关闭加载层
                $('button').prop('disabled', false);
                if (res.result.indexOf('SUCCESS')!=-1) {
                    layer.msg('上传成功');
                    addData = res.types;
                    table.reload('currentTableId', { data: addData });
                    console.log(res);
                    //后台赋值给界面
                    $("input[name='CustomerCode']").val(res.project.CustomerCode);
                    $("input[name='RFQCustomerCode']").val(res.project.RFQCustomerCode);
                    $("input[name='Customer']").val(res.project.Customer);
                    $("input[name='ProjectApp']").val(res.project.ProjectApp);
                    $("input[name='EndCustomer']").val(res.project.EndCustomer);
                    $("input[name='DesignLoc']").val(res.project.DesignLoc);
                    $("input[name='CostReduYear']").val(res.project.CostReduYear);
                    $("input[name='Rate']").val(res.project.Rate);
                    $("select[name='SuppCur']").val(res.project.SuppCur);
                    form.render();
                } else {
                    layer.msg('上传失败'+res.result);
                }
            },
            error: function () {
                // 请求异常回调
                layer.closeAll('loading'); // 关闭加载层
                $('button').prop('disabled', false);
                layer.msg('上传失败，请重试');
            }
        });
        // 监听搜索操作
        //form.on('submit(data-search-btn)', function (data) {
            //var result =  data.field ;
            //console.log(result.Customer);
            //layer.alert(result, {
            //    title: '最终的搜索信息'
            //});

            //执行搜索重载
        //    table.reload('currentTableId', {
        //        page: {
        //            curr: 1
        //        }
        //        , where: result,
        //        error: {

        //        }
                
        //    }, 'data');

        //    return false;
        //});

        //点击下载操作下载模版
        //form.on('button(data-download-btn)', function () {
        //    window.location.href = "批量上传.xlsx";
        //});

        /**
         * toolbar事件监听
         */
        table.on('toolbar(currentTableFilter)', function (obj) {
            if (obj.event === 'add') {   // 监听添加操作

                var index = layer.open({
                    title: '新建项目信息',
                    type: 2,
                    shade: 0.2,
                    maxmin: true,
                    shadeClose: true,
                    area: ['100%', '100%'],
                    content: '../Flow/AddProject.aspx',
                    success: function (layero,index) {
                        // 获取iframe页面的窗口对象
                        var iframeWindow = window['layui-layer-iframe' + index];
                        var iframeDoc = $(layero).find('iframe')[0].contentWindow.document;

                        // 在iframe页面中绑定提交事件
                        $(iframeDoc).find('.layui-form').on('submit', function (e) {
                            // 阻止表单默认提交
                            e.preventDefault();
                            return false;
                        });

                        // 监听iframe中的提交事件
                        iframeWindow.layui.form.on('submit(saveBtn)', function (data) {
                            var id = guid();
                            var field = data.field;
                            field.Id = id;

                            // 添加数据到表格
                            addData.push(field);

                            // 重载表格
                            table.reload('currentTableId', {
                                addData
                            });

                            // 关闭弹出层
                            layer.close(index);
                            return false;

                        });
                    },
                });
           // }
            
                //$(window).on("resize", function () {
                //    layer.full(index);
                //});
                //return false;
            } else if (obj.event === 'delete') {  // 监听删除操作
                var checkStatus = table.checkStatus('currentTableId')
                    , data = checkStatus.data;
                console.log(data);
                
                if (data.length == 0) {
                    layer.msg('请选择要删除的项', { icon: 0 });
                    return;
                }
                layer.confirm('确定删除选中的数据吗？', function () {
                    // 批量删除操作
                    for (var i = 0; i < data.length; i++) {
                        var index = addData.findIndex(item => item.Id === data[i].Id); // 找到对应数据索引
                        if (index !== -1) {
                            addData.splice(index, 1);  // 从数据源中删除选中的数据
                        }
                    }
                    table.reload('currentTableId', {
                        data: addData,
                        done: function (res, curr, count) {
                            if (curr > 1 && res.data.length === 0) {
                                curr = curr - 1;
                                table.reload('currentTableId', { page: { curr: curr }, })
                            }
                        }
                    });
                    layer.closeAll();
                    console.log(addData);
                });
                //layer.alert(JSON.stringify(data));
            }
        });

        //监听表格复选框选择
        table.on('checkbox(currentTableFilter)', function (obj) {
            //console.log(obj)
        });

        table.on('tool(currentTableFilter)', function (obj) {
            var data = obj.data;
            if (obj.event === 'edit') {

                

                var index = layer.open({
                    title: '编辑项目信息',
                    type: 2,
                    shade: 0.2,
                    maxmin: true,
                    shadeClose: true,
                    area: ['100%', '100%'],
                    content: '../Flow/EditProject.aspx',
                    success: function (layero, index) {
                        
                        // 获取iframe页面的窗口对象
                        var iframeWindow = window['layui-layer-iframe' + index];
                        iframeWindow.layui.form.val("editform", data);
                        

                        // 监听iframe中的提交事件
                        iframeWindow.layui.form.on('submit(saveBtn)', function (item) {
                            var field = item.field;
                            // 更新数据源
                            for (var i = 0; i < addData.length; i++) {
                                if (addData[i].Id == data.Id) {
                                    addData[i].MODEL = field.MODEL;
                                    addData[i].MODEL_DESC = field.MODEL_DESC;
                                    addData[i].EAU1 = field.EAU1;
                                    addData[i].EAU2 = field.EAU2;
                                    addData[i].EAU3 = field.EAU3;
                                    break;
                                }
                            }
                            // 重新渲染表格
                            table.reload('currentTableId', {
                                addData
                            });
                            layer.close(index);
                            return false;
                        });
                    },
                });
                //$(window).on("resize", function () {
                //    layer.full(index);
                //});
                //return false;
            } else if (obj.event === 'delete') {
                layer.confirm('真的删除行么', function (index) {
                        
                    // 找到全局数据中的对应项并删除
                    var indexInGlobal = addData.findIndex(function (item) {
                        return item.MODEL === data.MODEL;
                    });
                    if (indexInGlobal !== -1) {
                        addData.splice(indexInGlobal, 1);
                    }
                        table.reload('currentTableId', {
                            data: addData,
                            done: function (res, curr, count) {
                                if (curr > 1 && res.data.length === 0) {
                                    curr = curr - 1;
                                    table.reload('currentTableId', { page: {curr:curr},})
                                }
                            }
                        });
                        layer.closeAll();
                        console.log(addData);
                    
                });
            }
        });

    });</script>
