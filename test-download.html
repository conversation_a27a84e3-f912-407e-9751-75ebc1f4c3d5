<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>下载模板测试</title>
</head>
<body>
    <h2>下载模板测试页面</h2>
    <p>点击下面的链接测试下载功能：</p>
    
    <p><a href="ashx/ProjectController.ashx?action=DownloadTemplate" target="_blank">测试下载模板</a></p>
    
    <h3>测试说明：</h3>
    <ul>
        <li>如果下载成功，浏览器会开始下载"批量上传模板.xlsx"文件</li>
        <li>如果失败，页面会显示错误信息</li>
        <li>请检查浏览器的下载文件夹确认文件是否下载成功</li>
    </ul>
    
    <h3>可能的错误信息：</h3>
    <ul>
        <li><strong>"模板文件不存在，请联系管理员"</strong> - 文件未找到，需要将批量上传.xlsx文件复制到服务器</li>
        <li><strong>"下载失败：权限不足"</strong> - IIS权限问题，需要配置文件访问权限</li>
        <li><strong>"下载失败：路径错误"</strong> - 文件路径配置问题</li>
    </ul>
    
    <h3>文件应该放置的位置（按优先级）：</h3>
    <ol>
        <li>项目根目录：<code>[网站根目录]/批量上传.xlsx</code></li>
        <li>网络共享：<code>\\172.20.0.14\System Static\RFQSystem\批量上传.xlsx</code></li>
        <li>应用程序目录：<code>[应用程序目录]/批量上传.xlsx</code></li>
    </ol>
</body>
</html>
